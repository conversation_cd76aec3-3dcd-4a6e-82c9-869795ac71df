/**
 * Interface for dropdown option data structure
 */
export interface DropdownOption {
  ROW_ID: string;
  ID?: string;
  [key: string]: any; // Allow additional properties for different dropdown types
}

/**
 * Interface for dropdown search result
 */
export interface DropdownSearchResult {
  options: DropdownOption[];
  hasMore?: boolean;
  total?: number;
}

/**
 * Interface for dropdown API payload
 */
export interface DropdownApiPayload {
  _select: string[];
  _limit?: number;
  [key: string]: any; // Allow additional search criteria
}
