import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DropdownService } from '../services';
import { DropdownConfig, DropdownOption, DropdownSelectionEvent, DropdownState } from '../interfaces';

@Component({
  template: '' // Abstract component - no template
})
export abstract class BaseDropdownComponent implements OnInit, OnDestroy {
  @Input() config!: DropdownConfig;
  @Input() isViewMode: boolean = false;
  @Input() placeholder: string = '';
  
  @Output() selectionChange = new EventEmitter<DropdownSelectionEvent>();
  @Output() searchChange = new EventEmitter<string>();

  // Dropdown state
  state: DropdownState = {
    isOpen: false,
    isLoading: false,
    hasError: false
  };

  // Options and filtering
  allOptions: DropdownOption[] = [];
  filteredOptions: DropdownOption[] = [];
  
  // Search functionality
  private searchSubject = new Subject<string>();
  private searchTimeout: any;
  private destroy$ = new Subject<void>();
  
  // Services
  protected dropdownService = inject(DropdownService);
  protected cdr = inject(ChangeDetectorRef);

  ngOnInit(): void {
    this.initializeSearchSubscription();
    this.preloadData();
    this.updateFormControlState();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.clearSearchTimeout();
  }

  /**
   * Abstract method to get query builder ID - must be implemented by child components
   */
  protected abstract getQueryBuilderId(): string;

  /**
   * Abstract method to get API payload - can be overridden by child components
   */
  protected getApiPayload(): any {
    return { _select: ["ROW_ID"] };
  }

  /**
   * Initialize search subscription with debouncing
   */
  private initializeSearchSubscription(): void {
    this.searchSubject.pipe(
      debounceTime(this.config.searchTimeout || 300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.performSearch(searchTerm);
    });
  }

  /**
   * Preload dropdown data
   */
  private preloadData(): void {
    const queryBuilderId = this.getQueryBuilderId();
    if (queryBuilderId) {
      this.loadAllOptions();
    }
  }

  /**
   * Load all options from API
   */
  protected loadAllOptions(): void {
    const queryBuilderId = this.getQueryBuilderId();
    if (!queryBuilderId) return;

    this.state.isLoading = true;
    this.dropdownService.loadOptions(queryBuilderId, this.getApiPayload()).subscribe({
      next: (options) => {
        this.allOptions = options;
        this.filteredOptions = options;
        this.state.isLoading = false;
        this.state.hasError = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.hasError = true;
        this.state.errorMessage = 'Failed to load options';
        this.allOptions = [];
        this.filteredOptions = [];
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Perform search with filtering
   */
  private performSearch(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim() === '') {
      this.filteredOptions = this.allOptions;
    } else {
      this.filteredOptions = this.allOptions.filter(option =>
        this.dropdownService.getOptionDisplayText(option).toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    this.cdr.detectChanges();
  }

  /**
   * Handle input change events
   */
  onInputChange(event: Event): void {
    if (this.isViewMode || this.config.isDisabled) return;

    const input = event.target as HTMLInputElement;
    const value = input.value;
    
    this.searchSubject.next(value);
    this.searchChange.emit(value);
  }

  /**
   * Handle input focus events
   */
  onInputFocus(): void {
    if (this.isViewMode || this.config.isDisabled) return;

    const currentValue = this.config.form.get(this.config.fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.filteredOptions = this.allOptions;
    } else {
      this.searchSubject.next(currentValue);
    }
    this.state.isOpen = true;
  }

  /**
   * Handle input blur events
   */
  onInputBlur(): void {
    if (this.isViewMode || this.config.isDisabled) return;

    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.state.isOpen = false;
      this.cdr.detectChanges();
    }, 200);
  }

  /**
   * Toggle dropdown visibility
   */
  toggleDropdown(): void {
    if (this.isViewMode || this.config.isDisabled) return;

    if (!this.state.isOpen) {
      this.onInputFocus();
    } else {
      this.state.isOpen = false;
    }
  }

  /**
   * Select an option
   */
  selectOption(option: DropdownOption): void {
    if (this.isViewMode || this.config.isDisabled) return;

    const formControl = this.config.form.get(this.config.fieldName);
    if (formControl) {
      // Set the form control value
      const value = this.getOptionValue(option);
      formControl.setValue(value);
      formControl.markAsDirty();
      formControl.markAsTouched();
      formControl.updateValueAndValidity();

      // Update input display value
      setTimeout(() => {
        const inputElement = document.getElementById(this.config.fieldName) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = this.dropdownService.getOptionDisplayText(option);
        }
      }, 0);

      // Emit selection event
      this.selectionChange.emit({
        fieldName: this.config.fieldName,
        value: value,
        option: option
      });
    }

    this.state.isOpen = false;
    this.cdr.detectChanges();
  }

  /**
   * Get the value to set in form control - can be overridden by child components
   */
  protected getOptionValue(option: DropdownOption): any {
    return option.ROW_ID || option.ID;
  }

  /**
   * Update form control disabled state
   */
  private updateFormControlState(): void {
    const formControl = this.config.form.get(this.config.fieldName);
    if (formControl) {
      if (this.isViewMode || this.config.isDisabled) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }

  /**
   * Clear search timeout
   */
  private clearSearchTimeout(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = null;
    }
  }

  /**
   * Track by function for ngFor
   */
  trackByOptionId(_index: number, option: DropdownOption): string {
    return option.ROW_ID || option.ID || _index.toString();
  }
}
