/* Foreign Key Dropdown specific styles */

/* Import base dropdown styles */
@import '../../shared/base/base-dropdown.component.scss';

/* Form field styling */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

/* Required field indicator styling */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Override dropdown input container for foreign key fields */
.form-field .dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.form-field .dropdown-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
  padding: 8px 12px;
  height: 40px;
  border: 1px solid #DBDBDB;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
}

.form-field .dropdown-input-container .form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/* Disabled state for foreign key dropdown */
.form-field .dropdown-input-container .form-input[disabled],
.form-field .dropdown-input-container .dropdown-arrow-btn:disabled {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Foreign key specific dropdown list positioning */
.form-field .dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive styling for foreign key dropdown */
@media (max-width: 768px) {
  .form-field label {
    font-size: 11px;
  }

  .no-input-indicator {
    font-size: 10px;
  }

  .form-field .dropdown-input-container .form-input {
    font-size: 14px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .form-field label {
    font-size: 10px;
  }

  .no-input-indicator {
    font-size: 9px;
  }

  .form-field .dropdown-input-container .form-input {
    font-size: 13px;
    padding: 5px 8px;
  }
}
