/* ID Dropdown specific styles */

/* ID Input Container with Dropdown */
.id-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.id-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
  height: 40px;
  border: 1px solid #DBDBDB;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
}

.id-input-container .form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/* Invalid input styling */
.id-input-container .form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Disabled state */
.id-input-container .form-input[disabled],
.dropdown-arrow-btn:disabled {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

.dropdown-arrow-btn:disabled:hover {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  transform: none !important;
}

/* ID Dropdown List */
.id-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 40px; /* Account for arrow button width */
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.id-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.id-dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.id-dropdown-item:last-child {
  border-bottom: none;
}

.id-dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.id-dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.id-dropdown-error {
  padding: 12px 16px;
  color: #dc3545;
  font-style: italic;
  text-align: center;
}

/* Scrollbar styling for ID dropdown */
.id-dropdown::-webkit-scrollbar {
  width: 6px;
}

.id-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.id-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.id-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading state for dropdown */
.id-dropdown.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

/* Responsive styling */
@media (max-width: 768px) {
  .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .id-dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .id-dropdown-empty,
  .id-dropdown-loading,
  .id-dropdown-error {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .id-dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .id-dropdown-empty,
  .id-dropdown-loading,
  .id-dropdown-error {
    padding: 8px 10px;
    font-size: 12px;
  }
}
