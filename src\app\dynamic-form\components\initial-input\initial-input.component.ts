import { Component, Input, Output, EventEmitter, OnInit, <PERSON><PERSON><PERSON>roy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Import dropdown components
import { IdDropdownComponent } from '../dropdown/components/id-dropdown/id-dropdown.component';
import { DropdownConfig, DropdownSelectionEvent } from '../dropdown/shared/interfaces';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    IdDropdownComponent
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;

  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  private http = inject(HttpClient);

  ngOnInit() {
    // Component initialization
  }

  ngOnDestroy() {
    // Component cleanup
  }

  /**
   * Get dropdown configuration for ID dropdown component
   */
  getIdDropdownConfig(): DropdownConfig {
    return {
      fieldName: 'ID',
      form: this.form,
      isViewMode: false,
      placeholder: 'Enter ID',
      searchTimeout: 300,
      maxResults: 100
    };
  }

  /**
   * Handle validation change from ID dropdown
   */
  onValidationChange(showValidation: boolean): void {
    this.showValidation = showValidation;
    this.validationChange.emit(showValidation);
  }

  /**
   * Handle ID selection from dropdown
   */
  onIdSelectionChange(event: DropdownSelectionEvent): void {
    // The dropdown component already handles form control updates
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
    this.validationChange.emit(false);
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }



  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
  }
}
