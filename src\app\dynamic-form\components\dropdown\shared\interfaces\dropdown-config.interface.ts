import { FormGroup } from '@angular/forms';

/**
 * Configuration interface for dropdown components
 */
export interface DropdownConfig {
  fieldName: string;
  form: FormGroup;
  isViewMode?: boolean;
  isDisabled?: boolean;
  placeholder?: string;
  queryBuilderId?: string;
  searchTimeout?: number;
  maxResults?: number;
  allowEmpty?: boolean;
  customApiPayload?: any;
}

/**
 * Interface for dropdown events
 */
export interface DropdownSelectionEvent {
  fieldName: string;
  value: any;
  option: any;
}

/**
 * Interface for dropdown search events
 */
export interface DropdownSearchEvent {
  fieldName: string;
  searchTerm: string;
}

/**
 * Interface for dropdown state
 */
export interface DropdownState {
  isOpen: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
}
