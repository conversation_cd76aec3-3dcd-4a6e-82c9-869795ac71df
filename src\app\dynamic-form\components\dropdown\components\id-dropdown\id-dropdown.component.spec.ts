import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { IdDropdownComponent } from './id-dropdown.component';
import { DropdownService } from '../../shared/services';
import { environment } from '../../../../../environments/environment';

describe('IdDropdownComponent', () => {
  let component: IdDropdownComponent;
  let fixture: ComponentFixture<IdDropdownComponent>;
  let httpMock: HttpTestingController;
  let dropdownService: DropdownService;
  let formBuilder: FormBuilder;
  let testForm: FormGroup;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        IdDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ],
      providers: [
        DropdownService,
        FormBuilder
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IdDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    dropdownService = TestBed.inject(DropdownService);
    formBuilder = TestBed.inject(FormBuilder);

    // Create test form
    testForm = formBuilder.group({
      ID: ['', Validators.required]
    });

    // Set up component inputs
    component.config = {
      fieldName: 'ID',
      form: testForm,
      isViewMode: false,
      placeholder: 'Enter ID'
    };
    component.tableName = 'testTable';
    component.screenName = 'testScreen';
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct config', () => {
    component.ngOnInit();
    expect(component.config.fieldName).toBe('ID');
    expect(component.config.form).toBe(testForm);
  });

  it('should extract query builder ID from tableName', () => {
    component.tableName = 'testTable,extraPart';
    const queryBuilderId = component['getQueryBuilderId']();
    expect(queryBuilderId).toBe('testTable');
  });

  it('should use screenName when tableName is not available', () => {
    component.tableName = '';
    component.screenName = 'testScreen';
    const queryBuilderId = component['getQueryBuilderId']();
    expect(queryBuilderId).toBe('testScreen');
  });

  it('should load all IDs when input is empty', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const expectedUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      _select: ['ID'],
      _limit: 100
    });

    const mockResponse = [
      { ID: 'ID001' },
      { ID: 'ID002' },
      { ID: 'ID003' }
    ];
    req.flush(mockResponse);

    expect(component.allOptions).toEqual(mockResponse);
    expect(component.filteredOptions).toEqual(mockResponse);
  });

  it('should search for specific ID when input has value', () => {
    testForm.get('ID')?.setValue('ID001');
    component.ngOnInit();
    fixture.detectChanges();

    const expectedUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.body).toEqual({
      ID: { CT: 'ID001' },
      _select: ['ID'],
      _limit: 20
    });
  });

  it('should handle input change and update validation', () => {
    component.showValidation = true;
    spyOn(component.validationChange, 'emit');

    const inputElement = document.createElement('input');
    inputElement.value = 'ID001';
    const event = { target: inputElement } as any;

    component.onInputChange(event);

    expect(component.showValidation).toBe(false);
    expect(component.validationChange.emit).toHaveBeenCalledWith(false);
  });

  it('should select option and update form', () => {
    const mockOption = { ID: 'ID001', ROW_ID: 'ROW001' };
    spyOn(component.validationChange, 'emit');

    component.ngOnInit();
    component.selectOption(mockOption);

    expect(testForm.get('ID')?.value).toBe('ID001');
    expect(component.showValidation).toBe(false);
    expect(component.validationChange.emit).toHaveBeenCalledWith(false);
    expect(component.state.isOpen).toBe(false);
  });

  it('should return correct input class based on validation', () => {
    component.showValidation = false;
    expect(component.getInputClass()).toBe('');

    component.showValidation = true;
    expect(component.getInputClass()).toBe('invalid-input');
  });

  it('should validate ID correctly', () => {
    testForm.get('ID')?.setValue('');
    expect(component.isIdValid()).toBe(false);

    testForm.get('ID')?.setValue('ID001');
    expect(component.isIdValid()).toBe(true);

    testForm.get('ID')?.setValue('   ');
    expect(component.isIdValid()).toBe(false);
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const expectedUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent('Network error'));

    expect(component.state.hasError).toBe(true);
    expect(component.allOptions).toEqual([]);
    expect(component.filteredOptions).toEqual([]);
  });

  it('should disable input in view mode', () => {
    component.isViewMode = true;
    component.ngOnChanges();

    expect(component.config.isDisabled).toBe(true);
  });

  it('should track options by ID', () => {
    const option = { ID: 'ID001', ROW_ID: 'ROW001' };
    const trackResult = component.trackByOptionId(0, option);
    expect(trackResult).toBe('ID001');
  });

  it('should get option value preferring ID over ROW_ID', () => {
    const optionWithId = { ID: 'ID001', ROW_ID: 'ROW001' };
    expect(component['getOptionValue'](optionWithId)).toBe('ID001');

    const optionWithoutId = { ROW_ID: 'ROW001' };
    expect(component['getOptionValue'](optionWithoutId)).toBe('ROW001');
  });
});
