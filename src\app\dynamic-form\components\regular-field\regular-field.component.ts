import { Component, Input, Output, EventEmitter, On<PERSON><PERSON>roy, OnInit, OnC<PERSON><PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

// Import dropdown components
import { TypeDropdownComponent } from '../dropdown/components/type-dropdown/type-dropdown.component';
import { ForeignKeyDropdownComponent } from '../dropdown/components/foreign-key-dropdown/foreign-key-dropdown.component';
import { RegularDropdownComponent } from '../dropdown/components/regular-dropdown/regular-dropdown.component';
import { DropdownConfig, DropdownSelectionEvent } from '../dropdown/shared/interfaces';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    TypeDropdownComponent,
    ForeignKeyDropdownComponent,
    RegularDropdownComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnChanges {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)

  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  ngOnInit() {
    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
  }

  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  /**
   * Get dropdown configuration for child dropdown components
   */
  getDropdownConfig(): DropdownConfig {
    return {
      fieldName: this.field.fieldName,
      form: this.form,
      isViewMode: this.isViewMode,
      isDisabled: this.isViewMode || this.field.noInput,
      placeholder: `Search ${this.field.label?.trim() || this.field.fieldName}`,
      searchTimeout: 300,
      maxResults: 100
    };
  }

  /**
   * Handle dropdown selection changes
   */
  onDropdownSelectionChange(event: DropdownSelectionEvent): void {
    // Emit the field value change event for parent components
    this.fieldValueChange.emit({
      fieldName: event.fieldName,
      value: event.value
    });
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }
}
