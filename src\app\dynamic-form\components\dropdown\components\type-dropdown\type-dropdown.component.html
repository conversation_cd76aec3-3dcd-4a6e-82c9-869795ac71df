<div class="form-field">
  <label>{{ getFieldLabel() }}
    @if (isFieldMandatory()) {<span>*</span>}
    @if (isFieldReadOnly()) {<span class="no-input-indicator"> (Read Only)</span>}
  </label>

  <div class="dropdown-input-container">
    <input 
      [formControlName]="config.fieldName" 
      [id]="config.fieldName" 
      type="text" 
      class="form-input dropdown-input" 
      (input)="onInputChange($event)" 
      (focus)="onInputFocus()" 
      (blur)="onInputBlur()"
      [disabled]="isViewMode || config.isDisabled"
      [placeholder]="placeholder" />
    
    <!-- Arrow button to toggle dropdown -->
    <button 
      type="button" 
      class="dropdown-arrow-btn" 
      (click)="toggleDropdown()" 
      [disabled]="isViewMode || config.isDisabled"
      matTooltip="Show type options">
      <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
    </button>
    
    <!-- Dropdown list for filtered results -->
    @if (state.isOpen) {
      <div class="dropdown-list">
        @if (state.isLoading) {
          <div class="dropdown-loading">
            Loading types...
          </div>
        } @else if (state.hasError) {
          <div class="dropdown-error">
            {{ state.errorMessage || 'Error loading types' }}
          </div>
        } @else if (filteredOptions && filteredOptions.length > 0) {
          @for (option of filteredOptions; track trackByOptionId($index, option)) {
            <div class="dropdown-item" (click)="selectOption(option)">
              {{ option.ROW_ID }}
            </div>
          }
        } @else {
          <div class="dropdown-empty">
            No types found
          </div>
        }
      </div>
    }
  </div>
</div>
