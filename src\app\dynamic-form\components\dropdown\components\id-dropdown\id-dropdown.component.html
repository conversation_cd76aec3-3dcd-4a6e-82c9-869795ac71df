<div class="id-input-container">
  <input 
    [formControlName]="config.fieldName" 
    [id]="config.fieldName" 
    type="text" 
    class="form-input" 
    [class]="getInputClass()"
    (input)="onInputChange($event)" 
    (focus)="onInputFocus()" 
    (blur)="onInputBlur()"
    [disabled]="isViewMode || config.isDisabled"
    [placeholder]="placeholder || 'Enter ID'"
    required />
  
  <!-- Arrow button to toggle dropdown -->
  <button 
    type="button" 
    class="dropdown-arrow-btn" 
    (click)="toggleDropdown()" 
    [disabled]="isViewMode || config.isDisabled"
    matTooltip="Show ID suggestions">
    <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
  </button>
  
  <!-- Dropdown list for filtered results -->
  @if (state.isOpen) {
    <div class="id-dropdown">
      @if (state.isLoading) {
        <div class="id-dropdown-loading">
          Loading IDs...
        </div>
      } @else if (state.hasError) {
        <div class="id-dropdown-error">
          {{ state.errorMessage || 'Error loading IDs' }}
        </div>
      } @else if (filteredOptions && filteredOptions.length > 0) {
        @for (option of filteredOptions; track trackByOptionId($index, option)) {
          <div class="id-dropdown-item" (click)="selectOption(option)">
            {{ option.ID || option.ROW_ID }}
          </div>
        }
      } @else {
        <div class="id-dropdown-empty">
          No IDs found
        </div>
      }
    </div>
  }
</div>
