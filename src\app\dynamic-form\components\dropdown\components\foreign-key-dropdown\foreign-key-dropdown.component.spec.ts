import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { ForeignKeyDropdownComponent } from './foreign-key-dropdown.component';
import { DropdownService } from '../../shared/services';
import { environment } from '../../../../../environments/environment';

describe('ForeignKeyDropdownComponent', () => {
  let component: ForeignKeyDropdownComponent;
  let fixture: ComponentFixture<ForeignKeyDropdownComponent>;
  let httpMock: HttpTestingController;
  let dropdownService: DropdownService;
  let formBuilder: FormBuilder;
  let testForm: FormGroup;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ForeignKeyDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ],
      providers: [
        DropdownService,
        FormBuilder
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ForeignKeyDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    dropdownService = TestBed.inject(DropdownService);
    formBuilder = TestBed.inject(FormBuilder);

    // Create test form
    testForm = formBuilder.group({
      testField: ['', Validators.required]
    });

    // Set up component inputs
    component.field = {
      fieldName: 'testField',
      label: 'Test Field',
      mandatory: true,
      noInput: false,
      foreginKey: 'formDefinition'
    };
    component.config = {
      fieldName: 'testField',
      form: testForm,
      isViewMode: false,
      placeholder: 'Search Test Field'
    };
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct config', () => {
    component.ngOnInit();
    expect(component.config.fieldName).toBe('testField');
    expect(component.config.form).toBe(testForm);
  });

  it('should use formDefinition as query builder ID', () => {
    const queryBuilderId = component['getQueryBuilderId']();
    expect(queryBuilderId).toBe('formDefinition');
  });

  it('should load foreign key options on init', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const expectedUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=formDefinition`;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      _select: ['ROW_ID']
    });

    const mockResponse = [
      { ROW_ID: 'form1' },
      { ROW_ID: 'form2' },
      { ROW_ID: 'form3' }
    ];
    req.flush(mockResponse);

    expect(component.allOptions).toEqual(mockResponse);
    expect(component.filteredOptions).toEqual(mockResponse);
  });

  it('should select option and update form with ROW_ID', () => {
    const mockOption = { ROW_ID: 'form1' };

    component.ngOnInit();
    component.selectOption(mockOption);

    expect(testForm.get('testField')?.value).toBe('form1');
    expect(component.state.isOpen).toBe(false);
  });

  it('should return correct field label', () => {
    expect(component.getFieldLabel()).toBe('Test Field');

    component.multiIndex = 3;
    expect(component.getFieldLabel()).toBe('Test Field (3)');

    component.field.label = '';
    component.field.fieldName = 'testField';
    expect(component.getFieldLabel()).toBe('testField (3)');
  });

  it('should check if field is mandatory', () => {
    expect(component.isFieldMandatory()).toBe(true);

    component.field.mandatory = false;
    expect(component.isFieldMandatory()).toBe(false);
  });

  it('should check if field is read-only', () => {
    expect(component.isFieldReadOnly()).toBe(false);

    component.field.noInput = true;
    expect(component.isFieldReadOnly()).toBe(true);
  });

  it('should disable input when field is read-only', () => {
    component.field.noInput = true;
    component.ngOnChanges();

    expect(component.config.isDisabled).toBe(true);
  });

  it('should disable input in view mode', () => {
    component.isViewMode = true;
    component.ngOnChanges();

    expect(component.config.isDisabled).toBe(true);
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const expectedUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=formDefinition`;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent('Network error'));

    expect(component.state.hasError).toBe(true);
    expect(component.allOptions).toEqual([]);
    expect(component.filteredOptions).toEqual([]);
  });

  it('should filter options based on search term', () => {
    const mockOptions = [
      { ROW_ID: 'form1' },
      { ROW_ID: 'form2' },
      { ROW_ID: 'userForm' }
    ];

    component.allOptions = mockOptions;
    component.filteredOptions = mockOptions;

    // Simulate search
    const inputElement = document.createElement('input');
    inputElement.value = 'user';
    const event = { target: inputElement } as any;

    component.onInputChange(event);

    // Wait for debounce
    setTimeout(() => {
      expect(component.filteredOptions.length).toBe(1);
      expect(component.filteredOptions[0].ROW_ID).toBe('userForm');
    }, 350);
  });

  it('should track options by ROW_ID', () => {
    const option = { ROW_ID: 'form1' };
    const trackResult = component.trackByOptionId(0, option);
    expect(trackResult).toBe('form1');
  });

  it('should get option value as ROW_ID', () => {
    const option = { ROW_ID: 'form1', ID: 'someId' };
    expect(component['getOptionValue'](option)).toBe('form1');
  });
});
