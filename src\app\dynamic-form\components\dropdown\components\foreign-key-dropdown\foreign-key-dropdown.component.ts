import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BaseDropdownComponent } from '../../shared/base/base-dropdown.component';
import { DropdownConfig, DropdownOption } from '../../shared/interfaces';
import { DROPDOWN_QUERY_BUILDERS, DropdownType } from '../../shared/models';

@Component({
  selector: 'app-foreign-key-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './foreign-key-dropdown.component.html',
  styleUrl: './foreign-key-dropdown.component.scss'
})
export class ForeignKeyDropdownComponent extends BaseDropdownComponent implements OnInit, OnChanges {
  @Input() field!: any;
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)

  ngOnInit(): void {
    // Initialize the config if not provided
    if (!this.config) {
      this.config = {
        fieldName: this.field?.fieldName || '',
        form: this.config?.form || this.getFormFromParent(),
        isViewMode: this.isViewMode,
        isDisabled: this.isViewMode || this.field?.noInput,
        placeholder: this.placeholder || `Search ${this.field?.label?.trim() || this.field?.fieldName || 'Foreign Key'}`,
        searchTimeout: 300,
        maxResults: 100
      };
    }
    
    super.ngOnInit();
  }

  ngOnChanges(): void {
    if (this.config) {
      this.config.isViewMode = this.isViewMode;
      this.config.isDisabled = this.isViewMode || this.field?.noInput;
      this.updateFormControlState();
    }
  }

  /**
   * Get query builder ID for Foreign Key dropdown
   * Always uses 'formDefinition' for foreign key fields
   */
  protected getQueryBuilderId(): string {
    return DROPDOWN_QUERY_BUILDERS[DropdownType.FOREIGN_KEY];
  }

  /**
   * Get API payload for Foreign Key search
   * Uses standard ROW_ID selection
   */
  protected getApiPayload(): any {
    return {
      _select: ["ROW_ID"]
    };
  }

  /**
   * Override option value to use ROW_ID
   */
  protected getOptionValue(option: DropdownOption): any {
    return option.ROW_ID;
  }

  /**
   * Override select option to handle display value setting
   */
  override selectOption(option: DropdownOption): void {
    super.selectOption(option);
    
    // Set the input element's display value to the ROW_ID for foreign key fields
    setTimeout(() => {
      const inputElement = document.getElementById(this.config.fieldName) as HTMLInputElement;
      if (inputElement) {
        inputElement.value = option.ROW_ID;
      }
    }, 0);
  }

  /**
   * Get the display label for the field
   */
  getFieldLabel(): string {
    let label = this.field?.fieldName || '';
    
    if (this.field?.label?.trim()) {
      label = this.field.label.trim();
    }
    
    // Add multi-index if provided
    if (this.multiIndex) {
      label += ` (${this.multiIndex})`;
    }
    
    return label;
  }

  /**
   * Check if field is mandatory
   */
  isFieldMandatory(): boolean {
    return this.field?.mandatory === true;
  }

  /**
   * Check if field is read-only
   */
  isFieldReadOnly(): boolean {
    return this.field?.noInput === true;
  }

  /**
   * Get form from parent component (fallback method)
   */
  private getFormFromParent(): any {
    // This should be provided by the parent component
    // Return a minimal form structure as fallback
    return {
      get: () => null,
      controls: {}
    };
  }

  /**
   * Update form control disabled state
   */
  private updateFormControlState(): void {
    const formControl = this.config.form.get(this.config.fieldName);
    if (formControl) {
      if (this.isViewMode || this.config.isDisabled) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }
}
