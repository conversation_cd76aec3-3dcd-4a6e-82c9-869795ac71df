import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { DropdownModule } from './dropdown.module';
import { DropdownService } from './shared/services';
import { IdDropdownComponent } from './components/id-dropdown/id-dropdown.component';
import { TypeDropdownComponent } from './components/type-dropdown/type-dropdown.component';
import { ForeignKeyDropdownComponent } from './components/foreign-key-dropdown/foreign-key-dropdown.component';
import { RegularDropdownComponent } from './components/regular-dropdown/regular-dropdown.component';

describe('DropdownModule', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DropdownModule,
        HttpClientTestingModule,
        NoopAnimationsModule
      ]
    }).compileComponents();
  });

  it('should create the module', () => {
    expect(DropdownModule).toBeDefined();
  });

  it('should provide DropdownService', () => {
    const service = TestBed.inject(DropdownService);
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(DropdownService);
  });

  it('should export all dropdown components', () => {
    // Test that all components are available
    expect(IdDropdownComponent).toBeDefined();
    expect(TypeDropdownComponent).toBeDefined();
    expect(ForeignKeyDropdownComponent).toBeDefined();
    expect(RegularDropdownComponent).toBeDefined();
  });

  it('should have proper module configuration', () => {
    const module = new DropdownModule();
    expect(module).toBeTruthy();
  });

  describe('Component Integration', () => {
    it('should allow creating IdDropdownComponent', () => {
      const fixture = TestBed.createComponent(IdDropdownComponent);
      expect(fixture.componentInstance).toBeTruthy();
    });

    it('should allow creating TypeDropdownComponent', () => {
      const fixture = TestBed.createComponent(TypeDropdownComponent);
      expect(fixture.componentInstance).toBeTruthy();
    });

    it('should allow creating ForeignKeyDropdownComponent', () => {
      const fixture = TestBed.createComponent(ForeignKeyDropdownComponent);
      expect(fixture.componentInstance).toBeTruthy();
    });

    it('should allow creating RegularDropdownComponent', () => {
      const fixture = TestBed.createComponent(RegularDropdownComponent);
      expect(fixture.componentInstance).toBeTruthy();
    });
  });

  describe('Service Integration', () => {
    it('should inject DropdownService into components', () => {
      const fixture = TestBed.createComponent(IdDropdownComponent);
      const component = fixture.componentInstance;
      
      // Access the protected dropdownService property
      expect((component as any).dropdownService).toBeTruthy();
      expect((component as any).dropdownService).toBeInstanceOf(DropdownService);
    });

    it('should share the same service instance across components', () => {
      const fixture1 = TestBed.createComponent(IdDropdownComponent);
      const fixture2 = TestBed.createComponent(TypeDropdownComponent);
      
      const service1 = (fixture1.componentInstance as any).dropdownService;
      const service2 = (fixture2.componentInstance as any).dropdownService;
      
      expect(service1).toBe(service2);
    });
  });
});
