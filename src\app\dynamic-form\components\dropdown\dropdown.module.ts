import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Shared services
import { DropdownService } from './shared/services';

// Components
import { IdDropdownComponent } from './components/id-dropdown/id-dropdown.component';
import { TypeDropdownComponent } from './components/type-dropdown/type-dropdown.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    IdDropdownComponent
  ],
  providers: [
    DropdownService
  ],
  exports: [
    IdDropdownComponent
  ]
})
export class DropdownModule { }
