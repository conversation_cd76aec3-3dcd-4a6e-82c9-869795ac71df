import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DropdownService } from './dropdown.service';
import { environment } from '../../../../../environments/environment';

describe('DropdownService', () => {
  let service: DropdownService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DropdownService]
    });
    service = TestBed.inject(DropdownService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    service.clearCache(); // Clear cache after each test
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('loadOptions', () => {
    it('should load options from API', () => {
      const mockResponse = [
        { ROW_ID: 'option1', name: 'Option 1' },
        { ROW_ID: 'option2', name: 'Option 2' }
      ];
      const queryBuilderId = 'testQuery';

      service.loadOptions(queryBuilderId).subscribe(options => {
        expect(options).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ _select: ['ROW_ID'] });
      req.flush(mockResponse);
    });

    it('should use cached data on subsequent calls', () => {
      const mockResponse = [{ ROW_ID: 'option1' }];
      const queryBuilderId = 'testQuery';

      // First call
      service.loadOptions(queryBuilderId).subscribe();
      const req1 = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req1.flush(mockResponse);

      // Second call should use cache
      service.loadOptions(queryBuilderId).subscribe(options => {
        expect(options).toEqual(mockResponse);
      });

      // No additional HTTP request should be made
      httpMock.expectNone(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
    });

    it('should handle API errors gracefully', () => {
      const queryBuilderId = 'testQuery';

      service.loadOptions(queryBuilderId).subscribe(options => {
        expect(options).toEqual([]);
      });

      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req.error(new ErrorEvent('Network error'));
    });

    it('should handle non-array responses', () => {
      const queryBuilderId = 'testQuery';
      const mockResponse = { error: 'Invalid response' };

      service.loadOptions(queryBuilderId).subscribe(options => {
        expect(options).toEqual([]);
      });

      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req.flush(mockResponse);
    });
  });

  describe('searchOptions', () => {
    it('should filter options client-side', () => {
      const mockResponse = [
        { ROW_ID: 'apple', name: 'Apple' },
        { ROW_ID: 'banana', name: 'Banana' },
        { ROW_ID: 'apricot', name: 'Apricot' }
      ];
      const queryBuilderId = 'testQuery';

      service.searchOptions(queryBuilderId, 'ap').subscribe(options => {
        expect(options.length).toBe(2);
        expect(options[0].ROW_ID).toBe('apple');
        expect(options[1].ROW_ID).toBe('apricot');
      });

      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req.flush(mockResponse);
    });

    it('should return all options when search term is empty', () => {
      const mockResponse = [
        { ROW_ID: 'option1' },
        { ROW_ID: 'option2' }
      ];
      const queryBuilderId = 'testQuery';

      service.searchOptions(queryBuilderId, '').subscribe(options => {
        expect(options).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req.flush(mockResponse);
    });
  });

  describe('cache management', () => {
    it('should clear specific cache', () => {
      const mockResponse = [{ ROW_ID: 'option1' }];
      const queryBuilderId = 'testQuery';

      // Load data to cache
      service.loadOptions(queryBuilderId).subscribe();
      const req1 = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req1.flush(mockResponse);

      // Clear specific cache
      service.clearCache(queryBuilderId);

      // Next call should make new HTTP request
      service.loadOptions(queryBuilderId).subscribe();
      const req2 = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`);
      req2.flush(mockResponse);
    });

    it('should clear all cache', () => {
      const mockResponse = [{ ROW_ID: 'option1' }];

      // Load data for multiple queries
      service.loadOptions('query1').subscribe();
      service.loadOptions('query2').subscribe();

      const req1 = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=query1`);
      const req2 = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=query2`);
      req1.flush(mockResponse);
      req2.flush(mockResponse);

      // Clear all cache
      service.clearCache();

      // Next calls should make new HTTP requests
      service.loadOptions('query1').subscribe();
      service.loadOptions('query2').subscribe();

      httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=query1`);
      httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=query2`);
    });
  });

  describe('utility methods', () => {
    it('should get option display text preferring ID over ROW_ID', () => {
      const optionWithId = { ID: 'ID001', ROW_ID: 'ROW001' };
      expect(service.getOptionDisplayText(optionWithId)).toBe('ID001');

      const optionWithoutId = { ROW_ID: 'ROW001', name: 'Test' };
      expect(service.getOptionDisplayText(optionWithoutId)).toBe('ROW001');

      const optionWithOtherProps = { name: 'Test Name', description: 'Test Desc' };
      expect(service.getOptionDisplayText(optionWithOtherProps)).toBe('Test Name');
    });

    it('should get display keys excluding ROW_ID', () => {
      const option = { ROW_ID: 'row1', name: 'Test', description: 'Desc' };
      const keys = service.getDisplayKeys(option);
      expect(keys).toEqual(['name', 'description']);
      expect(keys).not.toContain('ROW_ID');
    });

    it('should handle empty or null options', () => {
      expect(service.getOptionDisplayText(null as any)).toBe('');
      expect(service.getOptionDisplayText({} as any)).toBe('');
      expect(service.getDisplayKeys({} as any)).toEqual([]);
    });
  });

  describe('preloadData', () => {
    it('should preload data for multiple query builders', () => {
      const queryBuilderIds = ['query1', 'query2', 'query3'];
      const mockResponse = [{ ROW_ID: 'option1' }];

      service.preloadData(queryBuilderIds);

      // Expect HTTP requests for all query builders
      queryBuilderIds.forEach(id => {
        const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=${id}`);
        req.flush(mockResponse);
      });
    });
  });
});
