import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BaseDropdownComponent } from '../../shared/base/base-dropdown.component';
import { DropdownConfig, DropdownOption } from '../../shared/interfaces';

@Component({
  selector: 'app-id-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './id-dropdown.component.html',
  styleUrl: './id-dropdown.component.scss'
})
export class IdDropdownComponent extends BaseDropdownComponent implements OnInit, OnChanges {
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;
  
  @Output() validationChange = new EventEmitter<boolean>();

  ngOnInit(): void {
    // Initialize the config if not provided
    if (!this.config) {
      this.config = {
        fieldName: 'ID',
        form: this.config?.form || this.getFormFromParent(),
        isViewMode: this.isViewMode,
        placeholder: this.placeholder || 'Enter ID',
        searchTimeout: 300,
        maxResults: 100
      };
    }
    
    super.ngOnInit();
  }

  ngOnChanges(): void {
    if (this.config) {
      this.config.isViewMode = this.isViewMode;
      this.config.isDisabled = this.isViewMode;
      this.updateFormControlState();
    }
  }

  /**
   * Get query builder ID for ID dropdown
   * Uses tableName or screenName, extracting part before comma if present
   */
  protected getQueryBuilderId(): string {
    const nameToUse = this.tableName || this.screenName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse || '';
  }

  /**
   * Get API payload for ID search
   * Uses ID field with Contains operator for search
   */
  protected getApiPayload(): any {
    const currentValue = this.config.form.get(this.config.fieldName)?.value || '';
    
    if (currentValue.trim() === '') {
      // Load all IDs
      return {
        _select: ["ID"],
        _limit: this.config.maxResults || 100
      };
    } else {
      // Search for specific ID
      return {
        ID: {
          CT: currentValue // CT = Contains operator
        },
        _select: ["ID"],
        _limit: 20
      };
    }
  }

  /**
   * Override option value to use ID field
   */
  protected getOptionValue(option: DropdownOption): any {
    return option.ID || option.ROW_ID;
  }

  /**
   * Handle input change with validation
   */
  override onInputChange(event: Event): void {
    super.onInputChange(event);
    
    const input = event.target as HTMLInputElement;
    const value = input.value;
    
    // Update validation state
    if (value && value.trim() !== '') {
      this.showValidation = false;
      this.validationChange.emit(false);
    }
  }

  /**
   * Override select option to handle validation
   */
  override selectOption(option: DropdownOption): void {
    super.selectOption(option);
    
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
    this.validationChange.emit(false);
  }

  /**
   * Get input CSS class based on validation state
   */
  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  /**
   * Check if ID is valid
   */
  isIdValid(): boolean {
    const idValue = this.config.form.get(this.config.fieldName)?.value;
    return idValue && idValue.trim() !== '';
  }

  /**
   * Get form from parent component (fallback method)
   */
  private getFormFromParent(): any {
    // This should be provided by the parent component
    // Return a minimal form structure as fallback
    return {
      get: () => null,
      controls: {}
    };
  }

  /**
   * Update form control disabled state
   */
  private updateFormControlState(): void {
    const formControl = this.config.form.get(this.config.fieldName);
    if (formControl) {
      if (this.isViewMode || this.config.isDisabled) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }
}
