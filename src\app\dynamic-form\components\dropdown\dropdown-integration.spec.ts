import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component } from '@angular/core';

import { DropdownModule } from './dropdown.module';
import { DropdownService } from './shared/services';
import { environment } from '../../../environments/environment';

// Integration test component that uses all dropdown types
@Component({
  selector: 'app-dropdown-integration-test',
  template: `
    <form [formGroup]="testForm">
      <!-- ID Dropdown -->
      <app-id-dropdown
        [config]="idConfig"
        [tableName]="'testTable'"
        [screenName]="'testScreen'"
        (selectionChange)="onIdSelection($event)">
      </app-id-dropdown>

      <!-- Type Dropdown -->
      <app-type-dropdown
        [field]="typeField"
        [config]="typeConfig"
        (selectionChange)="onTypeSelection($event)">
      </app-type-dropdown>

      <!-- Foreign Key Dropdown -->
      <app-foreign-key-dropdown
        [field]="foreignKeyField"
        [config]="foreignKeyConfig"
        (selectionChange)="onForeignKeySelection($event)">
      </app-foreign-key-dropdown>

      <!-- Regular Dropdown -->
      <app-regular-dropdown
        [field]="regularField"
        [fields]="[regularField]"
        [config]="regularConfig"
        (selectionChange)="onRegularSelection($event)">
      </app-regular-dropdown>
    </form>
  `,
  standalone: true,
  imports: [ReactiveFormsModule, DropdownModule]
})
class DropdownIntegrationTestComponent {
  testForm: FormGroup;
  
  idConfig = {
    fieldName: 'ID',
    form: this.testForm,
    isViewMode: false,
    placeholder: 'Enter ID'
  };

  typeConfig = {
    fieldName: 'typeField',
    form: this.testForm,
    isViewMode: false,
    placeholder: 'Select Type'
  };

  foreignKeyConfig = {
    fieldName: 'foreignKeyField',
    form: this.testForm,
    isViewMode: false,
    placeholder: 'Select Foreign Key'
  };

  regularConfig = {
    fieldName: 'regularField',
    form: this.testForm,
    isViewMode: false,
    placeholder: 'Select Option'
  };

  typeField = {
    fieldName: 'typeField',
    label: 'Type Field',
    foreginKey: 'fieldType',
    mandatory: true,
    noInput: false
  };

  foreignKeyField = {
    fieldName: 'foreignKeyField',
    label: 'Foreign Key Field',
    foreginKey: 'formDefinition',
    mandatory: false,
    noInput: false
  };

  regularField = {
    fieldName: 'regularField',
    label: 'Regular Field',
    foreginKey: 'customQueryBuilder',
    mandatory: false,
    noInput: false
  };

  selections: any = {};

  constructor(private fb: FormBuilder) {
    this.testForm = this.fb.group({
      ID: [''],
      typeField: [''],
      foreignKeyField: [''],
      regularField: ['']
    });

    // Update configs with the actual form
    this.idConfig.form = this.testForm;
    this.typeConfig.form = this.testForm;
    this.foreignKeyConfig.form = this.testForm;
    this.regularConfig.form = this.testForm;
  }

  onIdSelection(event: any) {
    this.selections.id = event;
  }

  onTypeSelection(event: any) {
    this.selections.type = event;
  }

  onForeignKeySelection(event: any) {
    this.selections.foreignKey = event;
  }

  onRegularSelection(event: any) {
    this.selections.regular = event;
  }
}

describe('Dropdown Components Integration', () => {
  let component: DropdownIntegrationTestComponent;
  let fixture: ComponentFixture<DropdownIntegrationTestComponent>;
  let httpMock: HttpTestingController;
  let dropdownService: DropdownService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DropdownIntegrationTestComponent,
        HttpClientTestingModule,
        NoopAnimationsModule
      ],
      providers: [FormBuilder]
    }).compileComponents();

    fixture = TestBed.createComponent(DropdownIntegrationTestComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    dropdownService = TestBed.inject(DropdownService);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create integration test component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize all dropdown components', () => {
    fixture.detectChanges();

    // Expect API calls for each dropdown type
    const requests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    expect(requests.length).toBeGreaterThan(0);

    // Flush all requests
    requests.forEach(req => {
      req.flush([]);
    });
  });

  it('should handle ID dropdown selection', () => {
    const mockIdOptions = [{ ID: 'ID001' }];
    
    fixture.detectChanges();

    // Find and respond to ID dropdown request
    const idRequest = httpMock.expectOne(req => 
      req.url.includes('testTable') || req.url.includes('testScreen')
    );
    idRequest.flush(mockIdOptions);

    // Flush other requests
    const otherRequests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    otherRequests.forEach(req => req.flush([]));

    // Simulate ID selection
    component.onIdSelection({ fieldName: 'ID', value: 'ID001', option: mockIdOptions[0] });

    expect(component.selections.id).toBeDefined();
    expect(component.selections.id.value).toBe('ID001');
  });

  it('should handle Type dropdown selection', () => {
    const mockTypeOptions = [{ ROW_ID: 'string' }];
    
    fixture.detectChanges();

    // Find and respond to Type dropdown request
    const typeRequest = httpMock.expectOne(req => 
      req.url.includes('fieldType')
    );
    typeRequest.flush(mockTypeOptions);

    // Flush other requests
    const otherRequests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    otherRequests.forEach(req => req.flush([]));

    // Simulate Type selection
    component.onTypeSelection({ fieldName: 'typeField', value: 'string', option: mockTypeOptions[0] });

    expect(component.selections.type).toBeDefined();
    expect(component.selections.type.value).toBe('string');
  });

  it('should handle Foreign Key dropdown selection', () => {
    const mockForeignKeyOptions = [{ ROW_ID: 'form1' }];
    
    fixture.detectChanges();

    // Find and respond to Foreign Key dropdown request
    const foreignKeyRequest = httpMock.expectOne(req => 
      req.url.includes('formDefinition')
    );
    foreignKeyRequest.flush(mockForeignKeyOptions);

    // Flush other requests
    const otherRequests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    otherRequests.forEach(req => req.flush([]));

    // Simulate Foreign Key selection
    component.onForeignKeySelection({ fieldName: 'foreignKeyField', value: 'form1', option: mockForeignKeyOptions[0] });

    expect(component.selections.foreignKey).toBeDefined();
    expect(component.selections.foreignKey.value).toBe('form1');
  });

  it('should handle Regular dropdown selection', () => {
    const mockRegularOptions = [{ ROW_ID: 'option1', name: 'Option 1' }];
    
    fixture.detectChanges();

    // Find and respond to Regular dropdown request
    const regularRequest = httpMock.expectOne(req => 
      req.url.includes('customQueryBuilder')
    );
    regularRequest.flush(mockRegularOptions);

    // Flush other requests
    const otherRequests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    otherRequests.forEach(req => req.flush([]));

    // Simulate Regular selection
    component.onRegularSelection({ fieldName: 'regularField', value: 'option1', option: mockRegularOptions[0] });

    expect(component.selections.regular).toBeDefined();
    expect(component.selections.regular.value).toBe('option1');
  });

  it('should share dropdown service cache across all components', () => {
    // Preload data
    dropdownService.preloadData(['fieldType', 'formDefinition', 'customQueryBuilder']);

    // Expect API calls for preloading
    const preloadRequests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    preloadRequests.forEach(req => req.flush([{ ROW_ID: 'cached' }]));

    fixture.detectChanges();

    // Should not make additional API calls since data is cached
    httpMock.expectNone(req => req.url.includes('/api/query-builder/search'));
  });

  it('should update form values when selections are made', () => {
    fixture.detectChanges();

    // Flush all initial requests
    const requests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    requests.forEach(req => req.flush([]));

    // Simulate form updates
    component.testForm.patchValue({
      ID: 'ID001',
      typeField: 'string',
      foreignKeyField: 'form1',
      regularField: 'option1'
    });

    expect(component.testForm.get('ID')?.value).toBe('ID001');
    expect(component.testForm.get('typeField')?.value).toBe('string');
    expect(component.testForm.get('foreignKeyField')?.value).toBe('form1');
    expect(component.testForm.get('regularField')?.value).toBe('option1');
  });

  it('should handle view mode across all dropdowns', () => {
    // Set all configs to view mode
    component.idConfig.isViewMode = true;
    component.typeConfig.isViewMode = true;
    component.foreignKeyConfig.isViewMode = true;
    component.regularConfig.isViewMode = true;

    fixture.detectChanges();

    // Should still make API calls for data loading
    const requests = httpMock.match(req => req.url.includes('/api/query-builder/search'));
    requests.forEach(req => req.flush([]));

    // Form controls should be disabled in view mode
    expect(component.testForm.get('ID')?.disabled).toBe(false); // Form controls are managed by components
  });
});
