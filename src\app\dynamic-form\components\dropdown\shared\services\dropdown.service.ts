import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { DropdownOption, DropdownSearchResult, DropdownApiPayload } from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class DropdownService {
  private http = inject(HttpClient);
  private apiCache: { [key: string]: DropdownOption[] } = {};

  /**
   * Load dropdown options with caching
   */
  loadOptions(queryBuilderId: string, payload?: DropdownApiPayload): Observable<DropdownOption[]> {
    const cacheKey = this.getCacheKey(queryBuilderId, payload);
    
    // Return cached data if available
    if (this.apiCache[cacheKey]) {
      return of(this.apiCache[cacheKey]);
    }

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const defaultPayload: DropdownApiPayload = {
      _select: ["ROW_ID"],
      ...payload
    };

    return this.http.post<any[]>(apiUrl, defaultPayload, { withCredentials: true }).pipe(
      map((response: any) => {
        const options = Array.isArray(response) ? response : [];
        this.apiCache[cacheKey] = options;
        return options;
      }),
      catchError(() => of([]))
    );
  }

  /**
   * Search dropdown options with filtering
   */
  searchOptions(queryBuilderId: string, searchTerm: string, payload?: DropdownApiPayload): Observable<DropdownOption[]> {
    return this.loadOptions(queryBuilderId, payload).pipe(
      map(options => this.filterOptions(options, searchTerm))
    );
  }

  /**
   * Filter options client-side
   */
  private filterOptions(options: DropdownOption[], searchTerm: string): DropdownOption[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return options;
    }

    const term = searchTerm.toLowerCase();
    return options.filter(option => {
      // Search in ROW_ID and other string properties
      return Object.values(option).some(value => 
        typeof value === 'string' && value.toLowerCase().includes(term)
      );
    });
  }

  /**
   * Get cache key for API requests
   */
  private getCacheKey(queryBuilderId: string, payload?: DropdownApiPayload): string {
    return `${queryBuilderId}_${JSON.stringify(payload || {})}`;
  }

  /**
   * Clear cache for specific query builder or all cache
   */
  clearCache(queryBuilderId?: string): void {
    if (queryBuilderId) {
      Object.keys(this.apiCache).forEach(key => {
        if (key.startsWith(queryBuilderId)) {
          delete this.apiCache[key];
        }
      });
    } else {
      this.apiCache = {};
    }
  }

  /**
   * Preload dropdown data for performance
   */
  preloadData(queryBuilderIds: string[]): void {
    queryBuilderIds.forEach(id => {
      this.loadOptions(id).subscribe();
    });
  }

  /**
   * Get display text for an option
   */
  getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For ID fields, prefer ID over ROW_ID
    if (option.ID) {
      return option.ID;
    }

    // For other fields, return ROW_ID or first non-ROW_ID property
    if (option.ROW_ID) {
      return option.ROW_ID;
    }

    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || '';
    }

    return '';
  }

  /**
   * Get all property keys for display (excluding ROW_ID)
   */
  getDisplayKeys(option: DropdownOption): string[] {
    return Object.keys(option).filter(key => key !== 'ROW_ID');
  }
}
