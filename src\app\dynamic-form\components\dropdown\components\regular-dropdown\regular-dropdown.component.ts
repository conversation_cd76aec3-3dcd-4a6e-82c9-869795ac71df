import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BaseDropdownComponent } from '../../shared/base/base-dropdown.component';
import { DropdownConfig, DropdownOption } from '../../shared/interfaces';

@Component({
  selector: 'app-regular-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './regular-dropdown.component.html',
  styleUrl: './regular-dropdown.component.scss'
})
export class RegularDropdownComponent extends BaseDropdownComponent implements OnInit, OnChanges {
  @Input() field!: any;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)

  ngOnInit(): void {
    // Initialize the config if not provided
    if (!this.config) {
      this.config = {
        fieldName: this.field?.fieldName || '',
        form: this.config?.form || this.getFormFromParent(),
        isViewMode: this.isViewMode,
        isDisabled: this.isViewMode || this.field?.noInput,
        placeholder: this.placeholder || `Search ${this.field?.label?.trim() || this.field?.fieldName || 'Options'}`,
        searchTimeout: 300,
        maxResults: 100
      };
    }
    
    super.ngOnInit();
  }

  ngOnChanges(): void {
    if (this.config) {
      this.config.isViewMode = this.isViewMode;
      this.config.isDisabled = this.isViewMode || this.field?.noInput;
      this.updateFormControlState();
    }
  }

  /**
   * Get query builder ID for Regular dropdown
   * Uses the field's foreginKey property
   */
  protected getQueryBuilderId(): string {
    // Extract the original field name from complex field names
    const originalFieldName = this.extractOriginalFieldName(this.config.fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName) || this.field;
    return field?.foreginKey || '';
  }

  /**
   * Get API payload for Regular dropdown search
   * Uses standard ROW_ID selection
   */
  protected getApiPayload(): any {
    return {
      _select: ["ROW_ID"]
    };
  }

  /**
   * Override option value to use ROW_ID
   */
  protected getOptionValue(option: DropdownOption): any {
    return option.ROW_ID;
  }

  /**
   * Override select option to handle display value setting for regular dropdowns
   */
  override selectOption(option: DropdownOption): void {
    super.selectOption(option);
    
    // Set the input element's display value to show all keys for regular dropdowns
    setTimeout(() => {
      const inputElement = document.getElementById(this.config.fieldName) as HTMLInputElement;
      if (inputElement) {
        const displayText = this.getOptionDisplayText(option);
        inputElement.value = displayText;
      }
    }, 0);
  }

  /**
   * Get display text for regular dropdown options
   * Shows all non-ROW_ID properties concatenated
   */
  getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    const keys = this.dropdownService.getDisplayKeys(option);
    if (keys.length > 0) {
      return keys.map(key => option[key]).filter(value => value).join(' ');
    }

    return option.ROW_ID || '';
  }

  /**
   * Get all property keys for display (excluding ROW_ID)
   */
  getKeys(option: DropdownOption): string[] {
    return this.dropdownService.getDisplayKeys(option);
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  /**
   * Get the display label for the field
   */
  getFieldLabel(): string {
    let label = this.field?.fieldName || '';
    
    if (this.field?.label?.trim()) {
      label = this.field.label.trim();
    }
    
    // Add multi-index if provided
    if (this.multiIndex) {
      label += ` (${this.multiIndex})`;
    }
    
    return label;
  }

  /**
   * Check if field is mandatory
   */
  isFieldMandatory(): boolean {
    return this.field?.mandatory === true;
  }

  /**
   * Check if field is read-only
   */
  isFieldReadOnly(): boolean {
    return this.field?.noInput === true;
  }

  /**
   * Get form from parent component (fallback method)
   */
  private getFormFromParent(): any {
    // This should be provided by the parent component
    // Return a minimal form structure as fallback
    return {
      get: () => null,
      controls: {}
    };
  }

  /**
   * Update form control disabled state
   */
  private updateFormControlState(): void {
    const formControl = this.config.form.get(this.config.fieldName);
    if (formControl) {
      if (this.isViewMode || this.config.isDisabled) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }
}
