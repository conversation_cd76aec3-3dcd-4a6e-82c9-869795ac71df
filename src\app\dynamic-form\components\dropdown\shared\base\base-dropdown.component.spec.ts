import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component } from '@angular/core';

import { BaseDropdownComponent } from './base-dropdown.component';
import { DropdownService } from '../services';
import { DropdownConfig } from '../interfaces';

// Test component that extends BaseDropdownComponent
@Component({
  selector: 'app-test-dropdown',
  template: `
    <div class="dropdown-input-container">
      <input 
        [formControlName]="config.fieldName" 
        [id]="config.fieldName" 
        type="text" 
        class="form-input dropdown-input" 
        (input)="onInputChange($event)" 
        (focus)="onInputFocus()" 
        (blur)="onInputBlur()"
        [disabled]="isViewMode || config.isDisabled"
        [placeholder]="placeholder" />
      
      <button 
        type="button" 
        class="dropdown-arrow-btn" 
        (click)="toggleDropdown()" 
        [disabled]="isViewMode || config.isDisabled">
        <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
      </button>
      
      @if (state.isOpen) {
        <div class="dropdown-list">
          @if (state.isLoading) {
            <div class="dropdown-loading">Loading...</div>
          } @else if (state.hasError) {
            <div class="dropdown-error">{{ state.errorMessage || 'Error loading options' }}</div>
          } @else if (filteredOptions && filteredOptions.length > 0) {
            @for (option of filteredOptions; track trackByOptionId($index, option)) {
              <div class="dropdown-item" (click)="selectOption(option)">
                {{ dropdownService.getOptionDisplayText(option) }}
              </div>
            }
          } @else {
            <div class="dropdown-empty">No options found</div>
          }
        </div>
      }
    </div>
  `,
  standalone: true,
  imports: [ReactiveFormsModule, MatIconModule, MatTooltipModule]
})
class TestDropdownComponent extends BaseDropdownComponent {
  protected getQueryBuilderId(): string {
    return 'testQueryBuilder';
  }

  protected getApiPayload(): any {
    return { _select: ['ROW_ID'] };
  }
}

describe('BaseDropdownComponent', () => {
  let component: TestDropdownComponent;
  let fixture: ComponentFixture<TestDropdownComponent>;
  let httpMock: HttpTestingController;
  let dropdownService: DropdownService;
  let formBuilder: FormBuilder;
  let testForm: FormGroup;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TestDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ],
      providers: [
        DropdownService,
        FormBuilder
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TestDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    dropdownService = TestBed.inject(DropdownService);
    formBuilder = TestBed.inject(FormBuilder);

    // Create test form
    testForm = formBuilder.group({
      testField: ['', Validators.required]
    });

    // Set up component config
    component.config = {
      fieldName: 'testField',
      form: testForm,
      isViewMode: false,
      placeholder: 'Test placeholder'
    };
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize and load options on init', () => {
    const mockOptions = [
      { ROW_ID: 'option1', name: 'Option 1' },
      { ROW_ID: 'option2', name: 'Option 2' }
    ];

    component.ngOnInit();
    fixture.detectChanges();

    const req = httpMock.expectOne((request) => 
      request.url.includes('testQueryBuilder')
    );
    expect(req.request.method).toBe('POST');
    req.flush(mockOptions);

    expect(component.allOptions).toEqual(mockOptions);
    expect(component.filteredOptions).toEqual(mockOptions);
    expect(component.state.isLoading).toBe(false);
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const req = httpMock.expectOne((request) => 
      request.url.includes('testQueryBuilder')
    );
    req.error(new ErrorEvent('Network error'));

    expect(component.state.hasError).toBe(true);
    expect(component.state.isLoading).toBe(false);
    expect(component.allOptions).toEqual([]);
    expect(component.filteredOptions).toEqual([]);
  });

  it('should filter options based on search input', fakeAsync(() => {
    const mockOptions = [
      { ROW_ID: 'apple', name: 'Apple' },
      { ROW_ID: 'banana', name: 'Banana' },
      { ROW_ID: 'apricot', name: 'Apricot' }
    ];

    component.allOptions = mockOptions;
    component.filteredOptions = mockOptions;

    const inputElement = document.createElement('input');
    inputElement.value = 'ap';
    const event = { target: inputElement } as any;

    component.onInputChange(event);
    tick(350); // Wait for debounce

    expect(component.filteredOptions.length).toBe(2);
    expect(component.filteredOptions[0].ROW_ID).toBe('apple');
    expect(component.filteredOptions[1].ROW_ID).toBe('apricot');
  }));

  it('should open dropdown on input focus', () => {
    const mockOptions = [{ ROW_ID: 'option1' }];
    component.allOptions = mockOptions;
    component.filteredOptions = mockOptions;

    component.onInputFocus();

    expect(component.state.isOpen).toBe(true);
  });

  it('should close dropdown on input blur', fakeAsync(() => {
    component.state.isOpen = true;

    component.onInputBlur();
    tick(250); // Wait for timeout

    expect(component.state.isOpen).toBe(false);
  }));

  it('should toggle dropdown visibility', () => {
    const mockOptions = [{ ROW_ID: 'option1' }];
    component.allOptions = mockOptions;
    component.filteredOptions = mockOptions;

    // Initially closed
    expect(component.state.isOpen).toBe(false);

    // Toggle open
    component.toggleDropdown();
    expect(component.state.isOpen).toBe(true);

    // Toggle closed
    component.toggleDropdown();
    expect(component.state.isOpen).toBe(false);
  });

  it('should select option and update form', () => {
    const mockOption = { ROW_ID: 'option1', name: 'Option 1' };
    spyOn(component.selectionChange, 'emit');

    component.selectOption(mockOption);

    expect(testForm.get('testField')?.value).toBe('option1');
    expect(component.state.isOpen).toBe(false);
    expect(component.selectionChange.emit).toHaveBeenCalledWith({
      fieldName: 'testField',
      value: 'option1',
      option: mockOption
    });
  });

  it('should disable interactions in view mode', () => {
    component.isViewMode = true;

    // Should not open dropdown
    component.onInputFocus();
    expect(component.state.isOpen).toBe(false);

    // Should not toggle dropdown
    component.toggleDropdown();
    expect(component.state.isOpen).toBe(false);

    // Should not handle input changes
    const inputElement = document.createElement('input');
    inputElement.value = 'test';
    const event = { target: inputElement } as any;
    
    spyOn(component.searchChange, 'emit');
    component.onInputChange(event);
    expect(component.searchChange.emit).not.toHaveBeenCalled();
  });

  it('should disable interactions when config is disabled', () => {
    component.config.isDisabled = true;

    // Should not open dropdown
    component.onInputFocus();
    expect(component.state.isOpen).toBe(false);

    // Should not select options
    const mockOption = { ROW_ID: 'option1' };
    const initialValue = testForm.get('testField')?.value;
    
    component.selectOption(mockOption);
    expect(testForm.get('testField')?.value).toBe(initialValue);
  });

  it('should track options by ID', () => {
    const option = { ROW_ID: 'option1', ID: 'id1' };
    const trackResult = component.trackByOptionId(0, option);
    expect(trackResult).toBe('id1');

    const optionWithoutId = { ROW_ID: 'option1' };
    const trackResult2 = component.trackByOptionId(0, optionWithoutId);
    expect(trackResult2).toBe('option1');
  });

  it('should emit search change events', () => {
    spyOn(component.searchChange, 'emit');

    const inputElement = document.createElement('input');
    inputElement.value = 'test search';
    const event = { target: inputElement } as any;

    component.onInputChange(event);

    expect(component.searchChange.emit).toHaveBeenCalledWith('test search');
  });

  it('should handle empty search terms', fakeAsync(() => {
    const mockOptions = [
      { ROW_ID: 'option1' },
      { ROW_ID: 'option2' }
    ];

    component.allOptions = mockOptions;
    component.filteredOptions = [];

    const inputElement = document.createElement('input');
    inputElement.value = '';
    const event = { target: inputElement } as any;

    component.onInputChange(event);
    tick(350);

    expect(component.filteredOptions).toEqual(mockOptions);
  }));
});
